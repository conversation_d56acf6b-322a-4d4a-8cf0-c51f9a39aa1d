import cn.hutool.core.lang.UUID;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.api.GamePropService;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.GamePropType;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto;
import io.github.cfgametech.Response;
import io.github.cfgametech.SDK;
import io.github.cfgametech.beans.GetChannelTokenRequest;
import io.github.cfgametech.beans.GetChannelTokenResponse;
import io.github.cfgametech.beans.GetGameServiceListRequest;
import io.github.cfgametech.beans.GetGameServiceListResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 */
@Slf4j
public class GamePropServiceTest {

    static {
//        -Dmetadata.region=cn
//                -Dmetadata.deploy.env=test
//                -Dmetadata.business.env=lizhi
//                -Dmetadata.service.name=lz_ocean_seal
//                -Dconf.env=office
//                -Dconf.key=lz_ocean_seal
//                -Dapp.name=lz_ocean_seal
//                -Dregion=cn
//                -DbusinessEnv=lizhi
//                -DCAT_HOME=/tmp
        System.setProperty("metadata.region", "cn");
        System.setProperty("metadata.deploy.env", "test");
        System.setProperty("metadata.service.name", "lz_ocean_seal_local");
        System.setProperty("conf.env", "cn");
        System.setProperty("region", "cn");
        System.setProperty("businessEnv", "lizhi");

    }

    public static final GamePropService gamePropService = ProxyBuilderContainer.PROXY_BUILDER.buildProxy(GamePropService.class);


    @Test
    public void grantGameProp() throws Exception {
        GamePropServiceProto.GrantGamePropParam.Builder builder = GamePropServiceProto.GrantGamePropParam.newBuilder();
        builder.setUserId(1431178837848312194L);
        builder.setChannelPropId("1");
        builder.setNum(1);
        builder.setUniqueId(UUID.fastUUID().toString());
        builder.setAppId("1895023737905397760");
        builder.setGameId("102");
        builder.setChannel(GameChannel.LUK);
        builder.setDuration(3600L);

        Result<GamePropServiceProto.ResponseGrantGameProp> result = gamePropService.grantGameProp(builder.build());
        log.info("rCode: {}", JsonUtil.dumps(result.rCode()));
        log.info("target: {}", JsonUtil.dumps(result.target()));
    }


    @Test
    public void getGamePropList() throws Exception {
        GamePropServiceProto.GetGamePropListParam.Builder builder = GamePropServiceProto.GetGamePropListParam.newBuilder();
        builder.setChannelGameId("102");
        builder.setType(GamePropType.PROP.getType());
        builder.setAppId("1895023737905397760");
        builder.setPageNumber(1);
        builder.setPageSize(10);

        Result<GamePropServiceProto.ResponseGetGamePropList> result = gamePropService.getGamePropList(builder.build());
        log.info("rCode: {}", JsonUtil.dumps(result.rCode()));
        log.info("target: {}", JsonUtil.dumps(result.target().getGamePropsList()));
    }

    @Test
    public void getUserPropStatus() throws Exception {
        GamePropServiceProto.GetUserPropStatusParam.Builder builder = GamePropServiceProto.GetUserPropStatusParam.newBuilder();
        builder.setUserId("1431178837848312194");
        builder.setChannelGameId("102");
        builder.setChannel(GameChannel.LUK);
        builder.setAppId("1895023737905397760");

        Result<GamePropServiceProto.ResponseGetUserPropStatus> result = gamePropService.getUserPropStatus(builder.build());
        log.info("rCode: {}", result.rCode());

        if (result.target() != null) {
            log.info("props count: {}", result.target().getPropsCount());
            for (int i = 0; i < result.target().getPropsCount(); i++) {
                GamePropServiceProto.UserPropStatus prop = result.target().getProps(i);
                log.info("prop[{}]: propId={}, type={}, num={}, expireTime={}",
                    i, prop.getPropId(), prop.getType(), prop.getNum(), prop.getExpireTime());
            }
        } else {
            log.info("target is null");
        }
    }

    @Test
    public void queryPropGrantStatus() throws Exception {
        GamePropServiceProto.QueryPropGrantStatusParam.Builder builder = GamePropServiceProto.QueryPropGrantStatusParam.newBuilder();
        builder.setUniqueId("c2d33a51-3c61-4a0b-99b9-a620652d36992222");
        builder.setChannelGameId("102");
        builder.setChannel(GameChannel.LUK);
        builder.setAppId("1895023737905397760");

        Result<GamePropServiceProto.ResponseQueryPropGrantStatus> result = gamePropService.queryPropGrantStatus(builder.build());
        log.info("rCode: {}", result.rCode());

        if (result.target() != null && result.target().hasGrantInfo()) {
            GamePropServiceProto.PropGrantStatusInfo grantInfo = result.target().getGrantInfo();
            log.info("grantInfo: appId={}, gameId={}, uniqueId={}, userId={}, status={}",
                grantInfo.getAppId(), grantInfo.getGameId(), grantInfo.getUniqueId(),
                grantInfo.getUserId(), grantInfo.getStatus());

            log.info("details count: {}", grantInfo.getDetailsCount());
            for (int i = 0; i < grantInfo.getDetailsCount(); i++) {
                GamePropServiceProto.PropGrantDetail detail = grantInfo.getDetails(i);
                log.info("detail[{}]: propId={}, num={}, duration={}",
                    i, detail.getPropId(), detail.getNum(), detail.getDuration());
            }
        } else {
            log.info("target is null or grantInfo not found");
        }
    }


}
